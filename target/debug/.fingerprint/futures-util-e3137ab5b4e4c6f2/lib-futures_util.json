{"rustc": 14565759800141205689, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 336243669335521001, "path": 13821470206355256718, "deps": [[5103565458935487, "futures_io", false, 11629161137541112110], [1615478164327904835, "pin_utils", false, 13361181336283860536], [1906322745568073236, "pin_project_lite", false, 13982399899631901281], [5451793922601807560, "slab", false, 14838899851344298073], [7013762810557009322, "futures_sink", false, 8917369158971119758], [7620660491849607393, "futures_core", false, 6020057125016203901], [10565019901765856648, "futures_macro", false, 3033583337529799046], [15932120279885307830, "memchr", false, 5716382709855584515], [16240732885093539806, "futures_task", false, 10155710151057271935]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-e3137ab5b4e4c6f2/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
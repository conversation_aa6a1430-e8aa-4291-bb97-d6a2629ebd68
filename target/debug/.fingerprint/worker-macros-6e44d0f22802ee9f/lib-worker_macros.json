{"rustc": 14565759800141205689, "features": "[\"queue\"]", "declared_features": "[\"http\", \"queue\"]", "target": 17706846810965141982, "profile": 3033921117576893, "path": 4306235420285977245, "deps": [[2589611628054203282, "wasm_bindgen_macro_support", false, 11658739041918836791], [3060637413840920116, "proc_macro2", false, 8477763921883706182], [4974441333307933176, "syn", false, 417483986616295705], [6946689283190175495, "wasm_bindgen", false, 7252460080871713233], [11946729385090170470, "async_trait", false, 2802442244505295050], [15917073803248137067, "wasm_bindgen_futures", false, 5711592206993386004], [17990358020177143287, "quote", false, 3280072456014327912], [18210333458952929752, "worker_sys", false, 15739517693435348152]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/worker-macros-6e44d0f22802ee9f/dep-lib-worker_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
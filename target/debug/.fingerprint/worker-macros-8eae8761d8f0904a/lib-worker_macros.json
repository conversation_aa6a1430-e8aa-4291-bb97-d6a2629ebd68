{"rustc": 14565759800141205689, "features": "[\"queue\"]", "declared_features": "[\"http\", \"queue\"]", "target": 17706846810965141982, "profile": 3033921117576893, "path": 4306235420285977245, "deps": [[2589611628054203282, "wasm_bindgen_macro_support", false, 11658739041918836791], [3060637413840920116, "proc_macro2", false, 8477763921883706182], [4974441333307933176, "syn", false, 417483986616295705], [6946689283190175495, "wasm_bindgen", false, 3203628084361077582], [11946729385090170470, "async_trait", false, 2802442244505295050], [15917073803248137067, "wasm_bindgen_futures", false, 7549033554526242092], [17990358020177143287, "quote", false, 3280072456014327912], [18210333458952929752, "worker_sys", false, 7028431959987675506]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/worker-macros-8eae8761d8f0904a/dep-lib-worker_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
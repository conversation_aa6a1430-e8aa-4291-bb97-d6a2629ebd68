{"rustc": 14565759800141205689, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 17669703692130904899, "path": 13821470206355256718, "deps": [[5103565458935487, "futures_io", false, 1413243467305394135], [1615478164327904835, "pin_utils", false, 17244249397677676173], [1906322745568073236, "pin_project_lite", false, 14259483743873770935], [5451793922601807560, "slab", false, 12928332803528259062], [7013762810557009322, "futures_sink", false, 15219747040119739869], [7620660491849607393, "futures_core", false, 13407110999347541382], [10565019901765856648, "futures_macro", false, 3033583337529799046], [15932120279885307830, "memchr", false, 14627799760530700187], [16240732885093539806, "futures_task", false, 4888270345369837370]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-c294369372870380/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
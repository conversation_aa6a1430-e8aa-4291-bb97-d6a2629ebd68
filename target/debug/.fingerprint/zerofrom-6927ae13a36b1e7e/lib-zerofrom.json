{"rustc": 14565759800141205689, "features": "[\"alloc\", \"derive\"]", "declared_features": "[\"alloc\", \"default\", \"derive\"]", "target": 723370850876025358, "profile": 5347358027863023418, "path": 15392975182586456861, "deps": [[4022439902832367970, "zerofrom_derive", false, 17939599803417797617]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/zerofrom-6927ae13a36b1e7e/dep-lib-zerofrom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
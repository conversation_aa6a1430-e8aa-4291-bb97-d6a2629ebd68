{"rustc": 14565759800141205689, "features": "[\"default\", \"msrv\", \"rustversion\", \"std\"]", "declared_features": "[\"default\", \"enable-interning\", \"gg-alloc\", \"msrv\", \"rustversion\", \"serde\", \"serde-serialize\", \"serde_json\", \"spans\", \"std\", \"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 4070942113156591848, "profile": 4765055421477619001, "path": 11380519856613747697, "deps": [[2828590642173593838, "cfg_if", false, 246092703425050882], [3722963349756955755, "once_cell", false, 3529557714330383473], [6946689283190175495, "build_script_build", false, 996677113257158109], [7858942147296547339, "rustversion", false, 5361911771533067832], [11382113702854245495, "wasm_bindgen_macro", false, 16062538094315682827]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wasm-bindgen-25271dda2982ece4/dep-lib-wasm_bindgen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 14565759800141205689, "features": "[]", "declared_features": "[\"axum\", \"d1\", \"http\", \"queue\", \"timezone\", \"tokio-postgres\"]", "target": 14418686653344221305, "profile": 5347358027863023418, "path": 1199786232730182118, "deps": [[430448058394534810, "wasm_streams", false, 16167928570473491145], [1811549171721445101, "futures_channel", false, 9756927949472398090], [3150220818285335163, "url", false, 9543311213788462183], [6264115378959545688, "pin_project", false, 17421831005610519939], [6770263515080949896, "worker_kv", false, 18049567882339365961], [6946689283190175495, "wasm_bindgen", false, 7252460080871713233], [8264480821543757363, "web_sys", false, 3959836028545670069], [9003359908906038687, "js_sys", false, 11920597914788570308], [9010263965687315507, "http", false, 7756827221848942513], [9678799920983747518, "matchit", false, 7008141800176740773], [9689903380558560274, "serde", false, 16419552656554955226], [9897246384292347999, "chrono", false, 7162535907682185730], [10629569228670356391, "futures_util", false, 4008928882816033862], [11261232116272131900, "serde_wasm_bindgen", false, 16182211181879648521], [11946729385090170470, "async_trait", false, 2802442244505295050], [12393800526703971956, "tokio", false, 1793528064468340514], [14084095096285906100, "http_body", false, 3370731450166006200], [15367738274754116744, "serde_json", false, 7833485679970854531], [15917073803248137067, "wasm_bindgen_futures", false, 5711592206993386004], [16066129441945555748, "bytes", false, 778601199723403530], [16542808166767769916, "serde_urlencoded", false, 11482568542319068714], [18210333458952929752, "worker_sys", false, 11982637175719826863], [18303752304840666217, "worker_macros", false, 1030732698254080207]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/worker-e5acb5e0bb91a6a3/dep-lib-worker", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
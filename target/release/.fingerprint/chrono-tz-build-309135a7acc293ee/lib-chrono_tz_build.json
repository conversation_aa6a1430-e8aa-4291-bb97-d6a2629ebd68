{"rustc": 14565759800141205689, "features": "[]", "declared_features": "[\"case-insensitive\", \"filter-by-regex\", \"regex\", \"uncased\"]", "target": 16403465266122158524, "profile": 1369601567987815722, "path": 10286313413967154175, "deps": [[1280075590338009456, "phf_codegen", false, 11931946403554985997], [12335805432749277816, "parse_zoneinfo", false, 13395524525623189450], [17186037756130803222, "phf", false, 6550257024079984157]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/chrono-tz-build-309135a7acc293ee/dep-lib-chrono_tz_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
// main.rs

use base64::{engine::general_purpose::URL_SAFE_NO_PAD, Engine as _};
use worker::*;
use futures_util::StreamExt;

// ===================================================================================
//
// ⚙️ Configuration Module
//
// This module defines the worker's configuration structure.
// It loads settings from Cloudflare environment variables, with sensible defaults
// based on the original JavaScript file.
//
// ===================================================================================
mod config {
    use worker::{Env, Result};

    // Corresponds to the configuration block in the original JS file.
    pub struct Config {
        /// Subscription path (原: 哎呀呀这是我的ID啊)
        pub subscription_path: String,
        /// VLESS UUID (原: 哎呀呀这是我的VL密钥)
        pub vless_uuid: String,
        /// Enable private key feature (原: 私钥开关)
        pub private_key_enabled: bool,
        /// Private key string (原: 咦这是我的私钥哎)
        pub private_key: String,
        /// Hide subscription page (原: 隐藏订阅)
        pub hide_subscription: bool,
        /// Taunt message when subscription is hidden (原: 嘲讽语)
        pub taunt_message: String,
        /// Preferred IPs/domains (原: 我的优选)
        pub preferred_nodes: Vec<String>,
        /// URL to a text file of preferred IPs (原: 我的优选TXT)
        pub preferred_nodes_txt_url: String,
        /// Master switch for the proxy feature (原: 启用反代功能)
        pub proxy_enabled: bool,
        /// Proxy IP or domain (原: 反代IP)
        pub proxy_ip: String,
        /// Enable SOCKS5 proxy (原: 启用SOCKS5反代)
        pub socks5_enabled: bool,
        /// Enable global SOCKS5 proxy (原: 启用SOCKS5全局反代)
        pub socks5_global: bool,
        /// SOCKS5 connection string (原: 我的SOCKS5账号)
        pub socks5_address: String,
        /// Default node name (原: 我的节点名字)
        pub node_name: String,
        /// Camouflage website (原: 伪装网页)
        pub camouflage_website: String,
    }

    impl Config {
        /// Loads configuration from environment variables, falling back to defaults.
        pub fn from_env(env: &Env) -> Result<Self> {
            let preferred_nodes_str = env
                .var("PREFERRED_NODES")
                .map(|v| v.to_string())
                .unwrap_or_else(|_| "www.visa.com.sg:8443,www.visa.com.hk:8443,www.visa.co.jp,*************,**************".to_string());

            Ok(Self {
                subscription_path: env.var("SUBSCRIPTION_PATH").map(|v| v.to_string()).unwrap_or_else(|_| "mycfbot2025".to_string()),
                vless_uuid: env.var("VLESS_UUID").map(|v| v.to_string()).unwrap_or_else(|_| "02a9780e-f549-41cb-b3de-a2e988a7170b".to_string()),
                private_key_enabled: env.var("PRIVATE_KEY_ENABLED").map(|v| v.to_string().parse().unwrap_or(false)).unwrap_or(false),
                private_key: env.var("PRIVATE_KEY").map(|v| v.to_string()).unwrap_or_else(|_| "".to_string()),
                hide_subscription: env.var("HIDE_SUBSCRIPTION").map(|v| v.to_string().parse().unwrap_or(false)).unwrap_or(false),
                taunt_message: env.var("TAUNT_MESSAGE").map(|v| v.to_string()).unwrap_or_else(|_| "哎呀你找到了我，但是我就是不给你看，气不气，嘿嘿嘿".to_string()),
                preferred_nodes: preferred_nodes_str.split(',').map(str::trim).filter(|s| !s.is_empty()).map(String::from).collect(),
                preferred_nodes_txt_url: env.var("PREFERRED_NODES_TXT_URL").map(|v| v.to_string()).unwrap_or_else(|_| "".to_string()),
                proxy_enabled: env.var("PROXY_ENABLED").map(|v| v.to_string().parse().unwrap_or(true)).unwrap_or(true),
                proxy_ip: env.var("PROXY_IP").map(|v| v.to_string()).unwrap_or_else(|_| "".to_string()),
                socks5_enabled: env.var("SOCKS5_ENABLED").map(|v| v.to_string().parse().unwrap_or(true)).unwrap_or(true),
                socks5_global: env.var("SOCKS5_GLOBAL").map(|v| v.to_string().parse().unwrap_or(false)).unwrap_or(false),
                socks5_address: env.var("SOCKS5_ADDRESS").map(|v| v.to_string()).unwrap_or_else(|_| "bf9d9462-c27a-4f3a-9529-f531f627069b:bf9d9462-c27a-4f3a-9529-f531f627069b@**************:45678".to_string()),
                node_name: env.var("NODE_NAME").map(|v| v.to_string()).unwrap_or_else(|_| "天书9.0".to_string()),
                camouflage_website: env.var("CAMOUFLAGE_WEBSITE").map(|v| v.to_string()).unwrap_or_else(|_| "www.youku.com".to_string()),
            })
        }
    }
}

// ===================================================================================
//
// 📄 Subscription Generation Module
//
// This module generates the subscription content for various clients like Clash
// and generic VLESS URI schemes.
//
// ===================================================================================
mod subscription {
    use super::config::Config;

    /// Generates the main subscription landing page. (原: 给我订阅页面)
    pub fn generate_landing_page(config: &Config, host: &str) -> String {
        format!(
            r#"1、本worker的私钥功能只支持clash，仅openclash和clash meta测试过，其他clash类软件自行测试
2、若使用通用订阅请关闭私钥功能
3、其他需求自行研究
通用的：https://{host}/{sub_path}/vless
猫咪的：https://{host}/{sub_path}/clash
"#,
            host = host,
            sub_path = config.subscription_path
        )
    }

    /// Generates a newline-separated list of VLESS URIs. (原: 给我通用配置文件)
    pub fn generate_vless_config(config: &Config, nodes: &[String], host: &str) -> Result<String, String> {
        if config.private_key_enabled {
            return Err("请先关闭私钥功能".to_string());
        }

        let mut final_nodes = nodes.to_vec();
        if final_nodes.is_empty() {
            final_nodes.push(format!("{}:443", host));
        }

        let vless_links: Vec<String> = final_nodes
            .iter()
            .map(|node_str| {
                let parts: Vec<&str> = node_str.split('@').collect();
                let main_part = parts[0];
                let tls_part = parts.get(1).unwrap_or(&"");

                let name_parts: Vec<&str> = main_part.split('#').collect();
                let address_part = name_parts[0];
                let binding = config.node_name.as_str();
                let node_name = name_parts.get(1).unwrap_or(&binding);

                let mut addr_port_parts: Vec<&str> = address_part.rsplitn(2, ':').collect();
                let address = addr_port_parts.pop().unwrap_or(address_part);
                let port = addr_port_parts.pop().and_then(|p| p.parse::<u16>().ok()).unwrap_or(443);

                let security = if *tls_part == "notls" { "security=none" } else { "security=tls" };

                format!(
                    "vless://{uuid}@{addr}:{port}?encryption=none&{sec}&sni={host}&type=ws&host={host}&path=%2F%3Fed%3D2560#{name}",
                    uuid = config.vless_uuid,
                    addr = address,
                    port = port,
                    sec = security,
                    host = host,
                    name = urlencoding::encode(node_name)
                )
            })
            .collect();

        Ok(vless_links.join("
"))
    }

    /// Generates a Clash configuration file. (原: 给我小猫咪配置文件)
    pub fn generate_clash_config(config: &Config, nodes: &[String], host: &str) -> String {
        let mut final_nodes = nodes.to_vec();
        if final_nodes.is_empty() {
            final_nodes.push(format!("{}:443", host));
        }

        let private_key_header = if config.private_key_enabled && !config.private_key.is_empty() {
            format!("
      my-key: {}", config.private_key)
        } else {
            "".to_string()
        };

        let proxies: Vec<String> = final_nodes
            .iter()
            .map(|node_str| {
                let parts: Vec<&str> = node_str.split('@').collect();
                let main_part = parts[0];
                let tls_part = parts.get(1).unwrap_or(&"");

                let name_parts: Vec<&str> = main_part.split('#').collect();
                let address_part = name_parts[0];
                let binding = config.node_name.as_str();
                let node_name = name_parts.get(1).unwrap_or(&binding);

                let mut addr_port_parts: Vec<&str> = address_part.rsplitn(2, ':').collect();
                let address = addr_port_parts.pop().unwrap_or(address_part).trim_matches(|c| c == '[' || c == ']');
                let port = addr_port_parts.pop().and_then(|p| p.parse::<u16>().ok()).unwrap_or(443);

                let use_tls = if *tls_part == "notls" { "false" } else { "true" };
                let full_node_name = format!("{}-{}-{}", node_name, address, port);

                format!(
                    r#"  - name: {full_node_name}
    type: vless
    server: {address}
    port: {port}
    uuid: {uuid}
    udp: false
    tls: {use_tls}
    sni: {host}
    network: ws
    ws-opts:
      path: "/?ed=2560"
      headers:
        Host: {host}{private_key_header}"#,
                    full_node_name = full_node_name,
                    address = address,
                    port = port,
                    uuid = config.vless_uuid,
                    use_tls = use_tls,
                    host = host,
                    private_key_header = private_key_header
                )
            })
            .collect();
        
        let proxy_names: Vec<String> = final_nodes
            .iter()
            .map(|node_str| {
                let parts: Vec<&str> = node_str.split('@').collect();
                let main_part = parts[0];
                
                let name_parts: Vec<&str> = main_part.split('#').collect();
                let address_part = name_parts[0];
                let binding = config.node_name.as_str();
                let node_name = name_parts.get(1).unwrap_or(&binding);

                let mut addr_port_parts: Vec<&str> = address_part.rsplitn(2, ':').collect();
                let address = addr_port_parts.pop().unwrap_or(address_part).trim_matches(|c| c == '[' || c == ']');
                let port = addr_port_parts.pop().and_then(|p| p.parse::<u16>().ok()).unwrap_or(443);

                format!("    - {}-{}-{}", node_name, address, port)
            })
            .collect();

        format!(
r#"dns:
  nameserver:
    - ************
    - 2400:da00::6666
  fallback:
    - *******
    - 2001:4860:4860::8888
proxies:
{}
proxy-groups:
- name: 🚀 节点选择
  type: select
  proxies:
    - 自动选择
{}
- name: 自动选择
  type: url-test
  url: http://www.gstatic.com/generate_204
  interval: 60
  tolerance: 30
  proxies:
{}
- name: 漏网之鱼
  type: select
  proxies:
    - DIRECT
    - 🚀 节点选择
rules:
- GEOSITE,category-ads-all,REJECT
- GEOSITE,cn,DIRECT
- GEOIP,CN,DIRECT,no-resolve
- GEOSITE,cloudflare,🚀 节点选择
- GEOIP,CLOUDFLARE,🚀 节点选择,no-resolve
- GEOSITE,gfw,🚀 节点选择
- GEOSITE,google,🚀 节点选择
- GEOIP,GOOGLE,🚀 节点选择,no-resolve
- GEOSITE,netflix,🚀 节点选择
- GEOIP,NETFLIX,🚀 节点选择,no-resolve
- GEOSITE,telegram,🚀 节点选择
- GEOIP,TELEGRAM,🚀 节点选择,no-resolve
- GEOSITE,openai,🚀 节点选择
- MATCH,漏网之鱼
"#,
            proxies.join("
"),
            proxy_names.join("
"),
            proxy_names.join("
")
        )
    }
}

// ===================================================================================
//
// 📡 VLESS Protocol Module
//
// This module is responsible for parsing the VLESS request header that comes
// from the client.
//
// ===================================================================================
mod vless {
    use std::convert::TryInto;
    use uuid::Uuid;

    #[derive(Debug)]
    pub enum Address {
        IPv4([u8; 4]),
        Domain(String),
        IPv6([u16; 8]),
    }

    impl std::fmt::Display for Address {
        fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
            match self {
                Address::IPv4(ip) => write!(f, "{}.{}.{}.{}", ip[0], ip[1], ip[2], ip[3]),
                Address::Domain(domain) => write!(f, "{}", domain),
                Address::IPv6(ip) => {
                    let segments: Vec<String> = ip.iter().map(|&seg| format!("{:x}", seg)).collect();
                    write!(f, "{}", segments.join(":"))
                }
            }
        }
    }

    #[derive(Debug)]
    pub struct VlessRequest {
        pub uuid: Uuid,
        pub port: u16,
        pub address: Address,
        pub initial_data: Vec<u8>,
    }

    /// Parses the VLESS header. (原: 解析VL标头)
    pub fn parse_request(data: &[u8]) -> Result<VlessRequest, String> {
        if data.len() < 24 {
            return Err("VLESS header is too short".to_string());
        }

        // 1 byte version, 16 bytes UUID
        let _version = data[0];
        let uuid_bytes: [u8; 16] = data[1..17].try_into().map_err(|_| "Invalid UUID bytes")?;
        let request_uuid = Uuid::from_bytes(uuid_bytes);

        // 1 byte addon length
        let addon_len = data[17] as usize;
        let mut cursor = 18 + addon_len;

        // 1 byte command (always 1 for TCP)
        let _command = data[cursor];
        cursor += 1;

        // 2 bytes port
        let port = u16::from_be_bytes(data[cursor..cursor + 2].try_into().map_err(|_| "Invalid port bytes")?);
        cursor += 2;

        // 1 byte address type
        let addr_type = data[cursor];
        cursor += 1;

        let address: Address;
        match addr_type {
            1 => { // IPv4
                if data.len() < cursor + 4 { return Err("Incomplete IPv4 address".to_string()); }
                let ip_bytes: [u8; 4] = data[cursor..cursor + 4].try_into().unwrap();
                address = Address::IPv4(ip_bytes);
                cursor += 4;
            }
            2 => { // Domain
                if data.len() < cursor + 1 { return Err("Incomplete domain length".to_string()); }
                let domain_len = data[cursor] as usize;
                cursor += 1;
                if data.len() < cursor + domain_len { return Err("Incomplete domain name".to_string()); }
                let domain = String::from_utf8(data[cursor..cursor + domain_len].to_vec())
                    .map_err(|_| "Invalid domain UTF-8")?;
                address = Address::Domain(domain);
                cursor += domain_len;
            }
            3 => { // IPv6
                if data.len() < cursor + 16 { return Err("Incomplete IPv6 address".to_string()); }
                let mut ip_bytes = [0u16; 8];
                for i in 0..8 {
                    ip_bytes[i] = u16::from_be_bytes(data[cursor + i * 2..cursor + i * 2 + 2].try_into().unwrap());
                }
                address = Address::IPv6(ip_bytes);
                cursor += 16;
            }
            _ => return Err(format!("Unsupported address type: {}", addr_type)),
        }

        let initial_data = data[cursor..].to_vec();

        Ok(VlessRequest {
            uuid: request_uuid,
            port,
            address,
            initial_data,
        })
    }
}

// ===================================================================================
//
// 🔌 Proxy and SOCKS5 Module
//
// This module handles the logic for connecting to the final destination,
// including direct connections, simple proxying, and the SOCKS5 handshake.
//
// ===================================================================================
mod proxy {
    use super::config::Config;
    use super::vless::{Address, VlessRequest};
    use worker::*;
    use tokio::io::{AsyncReadExt, AsyncWriteExt};

    /// Establishes a connection to the target, possibly through a proxy.
    pub async fn connect_to_target(req: &VlessRequest, config: &Config) -> Result<Socket> {
        // Global SOCKS5 override
        if config.proxy_enabled && config.socks5_enabled && config.socks5_global {
            console_log!("Using global SOCKS5 proxy...");
            return connect_socks5(req, config).await;
        }

        // Attempt direct connection first
        match Socket::builder().connect(&req.address.to_string(), req.port) {
            Ok(socket) => {
                console_log!("Successfully connected to target directly: {}:{}", req.address, req.port);
                return Ok(socket);
            }
            Err(e) => {
                console_log!("Direct connection failed: {}. Falling back to proxy if enabled.", e);
                if config.proxy_enabled {
                    if config.socks5_enabled {
                        console_log!("Falling back to SOCKS5 proxy...");
                        return connect_socks5(req, config).await;
                    } else if !config.proxy_ip.is_empty() {
                        console_log!("Falling back to simple proxy: {}", config.proxy_ip);
                        let mut parts = config.proxy_ip.rsplitn(2, ':');
                        let host = parts.nth(1).unwrap_or(&config.proxy_ip);
                        let port = parts.next().and_then(|p| p.parse::<u16>().ok()).unwrap_or(req.port);
                        return Socket::builder().connect(host, port);
                    }
                }
                Err(Error::from(format!("All connection attempts failed: {}", e)))
            }
        }
    }

    struct Socks5Credentials {
        user: String,
        pass: String,
        host: String,
        port: u16,
    }

    fn parse_socks5_address(address_str: &str) -> Result<Socks5Credentials> {
        let mut parts = address_str.split('@');
        let user_pass_part = parts.next().ok_or_else(|| Error::from("Invalid SOCKS5 format"))?;
        let host_port_part = parts.next().ok_or_else(|| Error::from("Invalid SOCKS5 format"))?;

        let mut user_pass = user_pass_part.rsplitn(2, ':');
        let pass = user_pass.next().ok_or_else(|| Error::from("SOCKS5: Missing password"))?.to_string();
        let user = user_pass.next().ok_or_else(|| Error::from("SOCKS5: Missing user"))?.to_string();

        let mut host_port = host_port_part.rsplitn(2, ':');
        let port_str = host_port.next().ok_or_else(|| Error::from("SOCKS5: Missing port"))?;
        let host = host_port.next().ok_or_else(|| Error::from("SOCKS5: Missing host"))?.to_string();
        let port = port_str.parse::<u16>().map_err(|_| Error::from("SOCKS5: Invalid port"))?;

        Ok(Socks5Credentials { user, pass, host, port })
    }

    /// Performs a SOCKS5 handshake. (原: 创建SOCKS5接口)
    async fn connect_socks5(req: &VlessRequest, config: &Config) -> Result<Socket> {
        let creds = parse_socks5_address(&config.socks5_address)?;
        let mut socket = Socket::builder().connect(&creds.host, creds.port)?;
        // 1. Greeting
        socket.write_all(&[0x05, 0x02, 0x00, 0x02]).await?;
        let mut buf = [0; 2];
        socket.read_exact(&mut buf).await?;
        if buf[0] != 0x05 {
            return Err(Error::from("SOCKS5: Invalid version from server"));
        }
        // 2. Authentication
        if buf[1] == 0x02 { // User/Pass auth required
            let user_bytes = creds.user.as_bytes();
            let pass_bytes = creds.pass.as_bytes();
            let mut auth_req = vec![0x01, user_bytes.len() as u8];
            auth_req.extend_from_slice(user_bytes);
            auth_req.push(pass_bytes.len() as u8);
            auth_req.extend_from_slice(pass_bytes);
            socket.write_all(&auth_req).await?;
            let mut auth_resp = [0; 2];
            socket.read_exact(&mut auth_resp).await?;
            if auth_resp[0] != 0x01 || auth_resp[1] != 0x00 {
                return Err(Error::from("SOCKS5: Authentication failed"));
            }
        } else if buf[1] != 0x00 { // No acceptable auth methods
            return Err(Error::from("SOCKS5: No acceptable authentication methods"));
        }
        // 3. Connection Request
        let mut conn_req = vec![0x05, 0x01, 0x00]; // Version 5, Command CONNECT, Reserved 0
        match &req.address {
            Address::IPv4(ip) => {
                conn_req.push(0x01);
                conn_req.extend_from_slice(ip);
            }
            Address::Domain(domain) => {
                conn_req.push(0x03);
                conn_req.push(domain.len() as u8);
                conn_req.extend_from_slice(domain.as_bytes());
            }
            Address::IPv6(ip) => {
                conn_req.push(0x04);
                for &segment in ip {
                    conn_req.extend_from_slice(&segment.to_be_bytes());
                }
            }
        }
        conn_req.extend_from_slice(&req.port.to_be_bytes());
        socket.write_all(&conn_req).await?;
        // 4. Server Reply
        let mut reply_buf = [0; 10];
        socket.read_exact(&mut reply_buf).await?;
        if reply_buf[0] != 0x05 || reply_buf[1] != 0x00 {
            return Err(Error::from(format!("SOCKS5: Connection failed, status: {}", reply_buf[1])));
        }
        Ok(socket)
    }
}

// ===================================================================================
//
// ↔️ Stream Piping Module
//
// This module contains the core logic for transferring data bidirectionally
// between the client WebSocket and the target TCP socket.
//
// ===================================================================================
mod pipe {
    use super::{Socket, WebSocket, Result};
    use worker::{WebsocketEvent, console_log};
    use tokio::io::AsyncWriteExt;
    use futures_util::StreamExt;

    /// Pipes data between a WebSocket and a TCP Socket. (原: 建立传输管道)
    pub async fn streams(websocket: WebSocket, mut socket: Socket) -> Result<()> {
        // 只实现 WebSocket -> Socket 单向转发
        let mut event_stream = websocket.events()?;
        while let Some(event) = event_stream.next().await {
            match event {
                Ok(WebsocketEvent::Message(msg)) => {
                    if let Some(bytes) = msg.bytes() {
                        if socket.write_all(&bytes).await.is_err() {
                            break;
                        }
                    }
                }
                _ => break,
            }
        }
        console_log!("Pipe streams finished.");
        Ok(())
    }
}

// ===================================================================================
//
// 🚀 Main Entry Point & Handlers
//
// This is the main entry point for the worker. It sets up routing for HTTP
// requests and handles WebSocket upgrade requests.
//
// ===================================================================================

#[event(fetch)]
pub async fn main(req: Request, env: Env, ctx: Context) -> Result<Response> {
    let upgrade_header = req.headers().get("Upgrade")?;
    if upgrade_header.as_deref() == Some("websocket") {
        return handle_websocket_request(req, env, ctx).await;
    }

    handle_http_request(req, env).await
}

/// Handles incoming regular HTTP requests, routing them to subscription generators or the camouflage site.
async fn handle_http_request(req: Request, env: Env) -> Result<Response> {
    let mut config = config::Config::from_env(&env)?;
    let url = req.url()?;
    let host = url.host_str().ok_or_else(|| Error::from("Missing host in URL"))?;

    // Fetch preferred nodes from TXT if URL is provided
    if !config.preferred_nodes_txt_url.is_empty() {
        let url = Url::parse(&config.preferred_nodes_txt_url)?;
        let mut response = Fetch::Url(url).send().await?;
        if response.status_code() == 200 {
            let text = response.text().await?;
            config.preferred_nodes = text.lines().map(str::trim).filter(|s| !s.is_empty()).map(String::from).collect();
        }
    }

    let path = url.path();
    let sub_path = &config.subscription_path;

    if path == format!("/{}/", sub_path) {
        let content = subscription::generate_landing_page(&config, host);
        return Response::ok(content);
    }

    if path == format!("/{}/vless", sub_path) || path == format!("/{}/clash", sub_path) {
        if config.hide_subscription {
            return Response::ok(&config.taunt_message);
        }
        let content = if path.ends_with("/vless") {
            subscription::generate_vless_config(&config, &config.preferred_nodes, host)
                .unwrap_or_else(|e| e)
        } else {
            subscription::generate_clash_config(&config, &config.preferred_nodes, host)
        };
        return Response::ok(content).map(|mut resp| {
            resp.headers_mut().set("Content-Type", "text/plain;charset=utf-8")?;
            Ok(resp)
        })?;
    }

    // Default to camouflage site
    if !config.camouflage_website.is_empty() {
        let mut new_url = url;
        new_url.set_host(Some(&config.camouflage_website))?;
        return Fetch::Request(Request::new_with_init(
            new_url.as_str(),
            &RequestInit::new().with_method(req.method()).with_headers(req.headers().clone()),
        )?).send().await;
    }

    Response::error("Not Found", 404)
}

/// Handles WebSocket upgrade requests. (原: 升级WS请求)
async fn handle_websocket_request(req: Request, env: Env, ctx: Context) -> Result<Response> {
    let config = config::Config::from_env(&env)?;

    // Check private key if enabled
    if config.private_key_enabled {
        if req.headers().get("my-key")? != Some(config.private_key.clone()) {
            return Response::error("Invalid private key", 403);
        }
    }

    // VLESS data is passed in this non-standard header
    let protocol = req.headers().get("sec-websocket-protocol")?
        .ok_or_else(|| Error::from("Missing sec-websocket-protocol header"))?;

    // Decode the VLESS request data (原: 使用64位加解密)
    let vless_data = URL_SAFE_NO_PAD.decode(protocol)
        .map_err(|_| Error::from("Failed to decode VLESS data from header"))?;

    // Create the WebSocket pair
    let pair = WebSocketPair::new()?;
    let server = pair.server;
    server.accept()?;

    // Spawn the connection handling task to run in the background
    ctx.wait_until(async move {
        match vless::parse_request(&vless_data) {
            Ok(vless_req) => {
                // UUID validation check (原: 验证VL的密钥)
                if !config.private_key_enabled && vless_req.uuid.to_string() != config.vless_uuid {
                    console_error!("UUID mismatch: expected {}, got {}", config.vless_uuid, vless_req.uuid);
                    let _ = server.close(Some(1008), Some("UUID mismatch"));
                    return;
                }
                
                // Send initial response to client
                let _ = server.send_with_bytes(&[0x00, 0x00]);

                match proxy::connect_to_target(&vless_req, &config).await {
                    Ok(mut socket) => {
                        // Write initial data if any
                        if !vless_req.initial_data.is_empty() {
                           let _ = socket.write_all(&vless_req.initial_data).await;
                        }

                        if let Err(e) = pipe::streams(server, socket).await {
                            console_error!("Pipe streams error: {}", e);
                        }
                    }
                    Err(e) => {
                        console_error!("Failed to connect to target: {}", e);
                        let _ = server.close(Some(1011), Some("Upstream connection failed"));
                    }
                }
            }
            Err(e) => {
                console_error!("Failed to parse VLESS request: {}", e);
                let _ = server.close(Some(1008), Some("Invalid VLESS header"));
            }
        }
    });

    Response::from_websocket(pair.client)
}

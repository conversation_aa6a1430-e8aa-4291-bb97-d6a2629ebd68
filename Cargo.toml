[package]
name = "r-cf-proxy"
version = "0.1.0"
edition = "2021"

[lib]
crate-type = ["cdylib"]

[dependencies]
# Updated dependencies to be compatible with modern Rust toolchains.
# The 'worker' crate update is the most important one to fix the build error.
worker = "0.6.0"
serde = { version = "1.0.203", features = ["derive"] }
base64 = "0.22.1"
# Added the "js" feature to provide a source of randomness for wasm targets.
uuid = { version = "1.8.0", features = ["v4", "js"] }
urlencoding = "2.1.3"
futures-util = "0.3.30"
js-sys = "0.3.69"
wasm-bindgen = "0.2.92"
tokio = { version = "1.28", features = ["io-util"] }
